# 1.流程图设计

![1752222279376](image/绘图设计方法技巧/1752222279376.png)

代码如下：

sequenceDiagram
    participant C as OpenAI 客户端 💻
    participant S as 本地 Flask 服务器 🐍
    participant T as 油猴脚本 🐵 (在 LMArena 页面)
    participant L as LMArena.ai 🌐

    C->>+S: 发送 /v1/chat/completions 请求
    S->>S: 准备注入任务和触发任务
    S-->>T: (轮询) 获取触发任务
    T->>T: 模拟输入触发器文本并发送
    T->>L: 发送包含触发器的请求
    T->>T: 拦截此请求
    S-->>T: (轮询) 获取注入任务
    T->>T: 使用注入数据替换请求内容
    T->>L: 发送净化后的真实请求
    L-->>T: (流式)返回模型响应
    T-->>S: (流式)转发响应数据块 📨
    S-->>-C: (流式)返回 OpenAI 格式的响应

---

# PPT绘图设计

![1752222469474](image/绘图设计方法技巧/1752222469474.png)

## 专业级HTML动态流程图绘制指令 (Professional Prompt for Dynamic HTML Flowchart Generation)

### 流程图设计通用建议：

* **风格统一：** **所有流程图在颜色方案（推荐科技蓝、商务灰、深绿等专业色系）、字体（无衬线体，如微软雅黑、Arial）、形状（圆角矩形为主）、箭头样式等方面保持高度一致。**
* **简洁明了：** **图中文字尽量精炼，突出关键词。详细解释放在图注或正文中。避免信息过载。**
* **专业美观：** **注意对齐、间距、平衡，确保图表整体视觉效果专业、整洁。**

### 第一部分：流程图内容与风格描述 (Flowchart Content & Style Description)

**1.1. 流程图总体目标与标题 (Overall Goal & Title of the Flowchart):**

* 例如: "绘制‘CyberSec核心技术赋能数据要素化与价值创造流程图’，旨在清晰展示原始数据经过核心技术赋能后形成数据要素并最终创造价值的完整过程。"
* 您的描述: **_________________________________________**

**1.2. 主要阶段/节点定义 (Main Stages/Nodes Definition):**

* 请逐个列出流程图中的主要阶段或节点。
* 对于每个阶段/节点，请提供：
* **唯一ID (Unique ID):** **(用于JS连接，例如** **rawDataStage**, **techBlock1** **等)**
* **标题 (Title):** **(显示在阶段/节点上的名称)**
* **核心内容 (Key Content):** **(例如，简短描述、关键特性、功能点列表等，建议使用项目符号** *** 项目点1**)
* 示例:
* ID: **stageOne**, Title: "数据采集", Content: *** 安全终端部署 * 合法/真实/完整性保障**
* 您的描述:

- ID: ____, Title: "____", Content: * ____ * ____ - ID: ____, Title: "____", Content: * ____ * ____ - ... (依此类推)

**1.3. 连接关系与逻辑流 (Connections & Logical Flow):**

* 请描述阶段/节点之间的连接。
* 对于每个连接，请说明：
* **源节点ID (Source Node ID):**
* **目标节点ID (Target Node ID):**
* **连接线样式 (Line Style):** **(例如:** **实线 (solid)**, **虚线 (dashed)**, **粗虚线 (thick-dashed)**)
* **箭头方向 (Arrow Direction):** **(例如:** **单向 (uni-directional) source指向target**, **双向 (bi-directional)**)
* **(可选) 连接线旁的标注文本 (Optional Label on Connector):**
* 示例:
* Source: **stageOne**, Target: **stageTwo**, Style: **solid**, Direction: **uni-directional**, Label: **(可选)**
* 您的描述:

- Source: ____, Target: ____, Style: ____, Direction: ____, Label (optional): "____" - Source: ____, Target: ____, Style: ____, Direction: ____, Label (optional): "____" - ... (依此类推)

**1.4. 总体布局偏好 (Overall Layout Preference):**

* 例如: "水平线性流程 (Horizontal Linear Flow)", "垂直层级结构 (Vertical Hierarchical Structure)", "中心辐射型 (Hub-and-Spoke)" 等。
* 您的描述: **_________________________________________**

**1.5. 期望视觉风格与特质 (Desired Visual Style & Qualities):**

* **关键词 (Keywords):** **例如:** **学术化 (academic)**, **严谨 (rigorous)**, **专业 (professional)**, **简洁清晰 (clean and clear)**, **视觉平衡 (visually balanced)**。
* **颜色偏好 (Color Palette Preference):** **(例如: "使用科技蓝、商务灰为主色调", "参考之前提供的[某图]的配色方案", "请AI推荐一套专业且适合学术报告的配色方案")**
* **字体偏好 (Font Preference - 可选):** **(例如: "无衬线字体，如Arial, Microsoft YaHei")**
* 您的描述: **_________________________________________**

**1.6. (可选) 容器大致尺寸预期 (Optional - Approximate Container Size Expectation):**

* 例如: **宽度约1200px，高度根据内容自适应**
* 您的描述: **_________________________________________**

---

### 第二部分：技术实现与代码规范要求 (Technical Implementation & Code Specifications)

**请AI严格遵循以下技术方案生成代码：**

**2.1. 输出格式 (Output Format):**

* **单一HTML文件:** **所有HTML结构、CSS样式（在** `<style>`**标签内）和JavaScript代码（在** `<script>`**标签内）需整合在同一个** **.html** **文件中，确保可直接在现代浏览器中运行。**

**2.2. HTML 结构 (HTML Structure):**

* **主容器 (Main Container):** **一个外层** **div** **作为流程图的总容器 (例如:** **class="flowchart-main-container"**), 并赋予其唯一ID (例如: **id="myFlowchartContainer"**)。
* **阶段/节点元素 (Stage/Node Elements):**
* 每个主要阶段/节点使用一个 **div** **元素表示 (例如:** **class="flow-stage"** **或** **class="node-block"**), 并必须拥有在 **1.2** **中定义的唯一** **id**。
* 阶段/节点内部结构：
* 标题使用 **h4** **或** **div** **(例如:** **class="stage-title"**).
* 内容列表使用 **ul** **和** **li** **(例如:** **class="stage-content-list"**).

**2.3. CSS 样式 (CSS Styling):**

* **布局 (Layout):**
* 主要使用 **Flexbox** **进行阶段/节点的布局 (例如，对于水平流程，主容器** **display: flex; justify-content: space-between;**)。
* 确保阶段/节点具有相同的外部高度，使用 **align-items: stretch;** **在父flex容器上。**
* **垂直内容填充:** **对于等高的flex子项（阶段/节点），其内部内容（如图文混排、列表）应能良好地垂直分布或居中，避免大量底部空白。这通常通过在子项内部也使用flex布局实现 (例如，阶段** **div** **设置为** **display: flex; flex-direction: column;**，其内容块 **flex-grow: 1; display: flex; justify-content: center; align-items: center;** **或** **justify-content: center;** **来垂直居中其子元素** **ul**)。
* **视觉风格 (Visual Style):**
* **专业色板:** **应用** **1.5** **中描述的颜色偏好或推荐一套。**
* **阶段/节点样式:** **定义边框 (颜色, 粗细,** **border-radius**), 背景色, 内边距 (**padding**), 外边距 (**margin**) 或间隙 (**gap** **for flex container), 以及细微的阴影 (**box-shadow**) 以增加层次感。**
* **文本样式:** **清晰的字体、合适的字号、行高、文本颜色。标题应比内容文本更突出。**
* **列表样式:** **移除默认列表符号，可使用自定义符号 (例如** **content: "▪";** **伪元素) 或无符号。**
* **容器样式:** **设定背景色、边框、内边距。**

**2.4. SVG 图形 (SVG Graphics):**

* **SVG画布 (SVG Canvas):** **在主容器内、所有HTML阶段/节点元素之上（通过** **z-index**）绝对定位一个SVG层 (例如: **`<svg class="connector-canvas">`**)，使其覆盖整个主容器区域。
* **交互穿透:** **设置** **pointer-events: none;** **在SVG画布上，允许鼠标事件穿透到下方的HTML元素。**
* **箭头定义 (**`<defs>`**):** **在SVG的** **`<defs>`** **部分定义可重用的箭头样式 (**`<marker>`**)。**
* 至少定义一个标准的单向箭头 (例如: **id="defaultArrowhead"**).
* (可选) 定义用于双向箭头的起始端反向箭头 (例如: **id="bidirectionalStartArrowhead"**).
* 箭头应包含 **viewBox**, **refX**, **refY**, **markerUnits**, **markerWidth**, **markerHeight**, **orient="auto-start-reverse"** **(或** **auto** **for start markers) 属性。箭头形状使用** **`<path>`**。
* **连接线:** **连接线本身将由JavaScript动态生成和添加。静态SVG部分主要包含** `<defs>`**。**

**2.5. JavaScript 动态连接 (JavaScript for Dynamic Connections):**

* **执行时机:** **脚本应在** **window.addEventListener('load', () => { ... });** **或类似事件后执行，确保所有HTML元素已加载并渲染完毕。**
* **核心功能函数:**
* **getPortPosition(elementId, portSide, relativeToContainer, offsetPercentage = 0.5, edgeOverlap = 0)**:
* **elementId**: 目标HTML元素的ID。
* **portSide**: 字符串，如 **'left'**, **'right'**, **'top'**, **'**bottom**'**，表示连接点在元素的哪条边。
* **relativeToContainer**: 主流程图容器的DOM元素，用于计算相对坐标。
* **offsetPercentage**: (可选, 默认0.5) 沿边的偏移百分比 (0.0为起点, 0.5为中点, 1.0为终点)。
* **edgeOverlap**: (可选, 默认0) 像素值，用于控制连接线端点“延伸入”或“退后于”元素边界的距离。正值表示延伸入元素内部，负值表示在边界外。
* 函数返回 **{x, y}** **对象，包含相对于** **relativeToContainer** **的SVG坐标。**
* **drawLine(p1, p2, options = {})**:
* **p1**, **p2**: **{x, y}** **对象，分别表示线的起点和终点坐标。**
* **options**: (可选) 对象，包含:
* **stroke**: 线条颜色 (默认: **#555** **或主题色)。**
* **strokeWidth**: 线条宽度 (默认: **2px** **或** **1.5px**)。
* **markerEndId**: 应用于线条末端的marker ID (例如: **defaultArrowhead**)。
* **markerStartId**: (可选) 应用于线条始端的marker ID (用于双向箭头)。
* **dashArray**: (可选) SVG **stroke-dasharray** **值 (例如:** **'5 3'** **用于虚线)。**
* **id**: (可选) 为SVG line元素设置id。
* 函数动态创建SVG **`<line>`** **(或根据需要创建** **`<path>`**) 元素并添加到SVG画布。
* **连接逻辑:**
* 遍历 **1.3** **中定义的连接关系。**
* 对每个连接，使用 **getPortPosition** **计算源节点和目标节点的准确连接点坐标。**
* 使用 **drawLine** **函数绘制连接线，并根据描述应用样式（实线/虚线、箭头）。**
* **确保主连接线（例如阶段之间的主流程线）具有足够的视觉突出度 (如通过** **edgeOverlap** **和** **strokeWidth** **控制)。**

---

# 产业化/产品效果图设计

真实项目背景 + 模糊脱敏 + 扁平卡片指标“”的组合，具体如下：

### “设计元素—呈现方式—关联要点信息”方法论

> 适用于 **项目方案示意图 / 产品落地成效图 / 产业化效果图** 等多种场景。核心目标：帮助评委或决策者用最短时间感知  **真实投入、技术含金量与商业价值** 。

| 序号 | 设计元素（What）              | 呈现方式（How）                             | 关联要点信息（Why & Which）                                                               | 价值点                                     |
| ---- | ----------------------------- | ------------------------------------------- | ----------------------------------------------------------------------------------------- | ------------------------------------------ |
| 1    | **真实场景背景**        | 半透明/低饱和度大图作底；实拍或高度写实渲染 | ‣ 项目实施地点、生产线、终端场景‣ 设备类型（如机械臂、AGV、服务器机柜）                 | 从画面第一眼建立“非概念图”的信任感       |
| 2    | **时间轴 (Timeline)**   | 单线或阶梯式箭头，节点配日期 & 动作         | ‣ 合同签订、首版方案、集成测试、生产部署、验收交付等关键日期‣ 里程碑描述精炼到 2–3 词  | 让评委快速了解进度与周期，对比同类项目效率 |
| 3    | **里程碑流程块**        | 方块/圆角卡片 + 连线；可用简易图标          | ‣ 主要工作包（方案设计 / 数据集成 / 试运行 / 培训…）‣ 每块仅留 1 个动作动词 + 1 个名词 | 显示“做了什么”，避免口号式堆砌           |
| 4    | **量化 KPI / 成效指标** | 数字 + 对比箭头 / 圆环图 / 微型柱状图       | ‣ 前后对比：故障率、修复周期、能耗、产能‣ 行业平均值或合同要求（若有）                  | 用硬数字佐证“含金量”，降低主观评判       |
| 5    | **风险&安全亮点**       | 盾牌、警报等安全图标；旁置小块              | ‣ 0 重大事故、检测延迟 < x s、合规认证等级‣ 关键防护措施（加密、隔离、审计）            | 评委普遍关注“可靠性”，用视觉锚点突出     |
| 6    | **真实性印记**          | 合同页水印、客户 Logo、证书缩略图           | ‣ 去除敏感信息，仅保留可公开徽标/证书号                                                  | 进一步打消“PPT 造梦”顾虑                 |
| 7    | **视觉识别系统 (VIS)**  | 行业标准配色 + 英文字体或公司字体库         | ‣ 工业蓝+安全橙 / 科技紫+白 / 清洁能源绿…‣ 字体保持同一字重体系                        | 保持品牌一致性，兼顾可读性与专业感         |
| 8    | **可拓展占位**          | 预留空白 / 虚线框                           | ‣ 后期可插入客户案例、专家背书、ROI 图                                                   | 提前规划二次编辑或不同版本输出             |
| 9    | **脚注/数据来源**       | 3–4 pt 小字或角标                          | ‣ 数据归属年份、检测机构、合同条款页码                                                   | 对审查严谨的评审会尤为重要                 |
| 10   | **文件格式与输出**      | AI/SVG + PNG；分辨率 ≥ 300 dpi             | ‣ 便于后续多语言替换、线下大幅打印                                                       | 保证跨场景复用、品质不损失                 |

---

#### 快速落地 3 步曲

1. **拆解资料 → 元素清单**
   * 逐页扫合同/技术协议/测试报告，标记所有 **可量化指标** 与  **阶段节点** 。
   * 用上表 1–10 项作检查表，确保没有遗漏关键可信度信号。
2. **布置画布 → 视觉骨架**
   * 16:9 屏幕版、A3 打印版分别设定安全区。
   * 先放场景背景，再画时间轴与里程碑；右侧或下方腾出 KPI 模块固定区。
   * 空白处预留未来可增删部分（客户 Logo、证书、案例照片等）。
3. **填充数据 → 统一风格**
   * 全图限定 2–3 主色 + 1 强调色；字体大小成“标题 32 pt / 次级 24 pt / 正文 18 pt / 脚注 10 pt”金字塔。
   * 数字采用  **粗体 + 对比色** ；单位保持统一（s、days、%）。
   * 最后检查： **是否一眼能读到 3 个核心指标** ？ **是否能在 30 秒内说清项目故事** ？

> **应用范围**
>
> * **产品级 Roadmap** ：时间轴 + 里程碑块 + 未来功能灰色占位。
> * **产业化示意图** ：工厂/园区俯视图作背景，节点改为“产线1”“产线2”“云平台”。
> * **融资 Pitch 成果页** ：背景换为用户场景，KPI 改为 ARR / 客户增长。
> * **政府评审材料** ：增加法规遵从性、示范效益、区域带动图层。

![1752222851653](image/绘图设计方法技巧/1752222851653.png)

---

![1753251322032](image/绘图设计方法技巧/1753251322032.png)
